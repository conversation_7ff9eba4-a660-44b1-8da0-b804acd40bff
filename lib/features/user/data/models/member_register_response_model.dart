import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/member_register_response.dart';

part 'member_register_response_model.freezed.dart';
part 'member_register_response_model.g.dart';

@freezed
abstract class MemberRegisterResponseModel with _$MemberRegisterResponseModel {
  const factory MemberRegisterResponseModel({
    required bool status,
    @JsonKey(name: 'error_message') String? errorMessage,
    @JsonKey(name: 'error_code') String? errorCode,
    Map<String, dynamic>? data,
    @Json<PERSON>ey(name: 'api_version') required String apiVersion,
    @Json<PERSON>ey(name: 'validation_errors') Map<String, List<String>>? validationErrors,
  }) = _MemberRegisterResponseModel;

  factory MemberRegisterResponseModel.fromJson(Map<String, dynamic> json) {
    // Handle validation errors from nested data structure
    Map<String, List<String>>? validationErrors;
    if (json['data'] is Map<String, dynamic>) {
      final data = json['data'] as Map<String, dynamic>;
      if (data['validation_errors'] is Map<String, dynamic>) {
        final errors = data['validation_errors'] as Map<String, dynamic>;
        validationErrors = errors.map((key, value) {
          if (value is List) {
            return MapEntry(key, value.cast<String>());
          }
          return MapEntry(key, [value.toString()]);
        });
      }
    }

    return MemberRegisterResponseModel(
      status: json['status'] ?? false,
      errorMessage: json['error_message'],
      errorCode: json['error_code']?.toString(),
      data: json['data'] is Map<String, dynamic> ? json['data'] : null,
      apiVersion: json['api_version'] ?? '',
      validationErrors: validationErrors,
    );
  }
}

extension MemberRegisterResponseModelX on MemberRegisterResponseModel {
  MemberRegisterResponse toEntity() => MemberRegisterResponse(
        status: status,
        errorMessage: errorMessage,
        errorCode: errorCode,
        data: data,
        apiVersion: apiVersion,
        validationErrors: validationErrors,
      );
}
