// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'member_register_response_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MemberRegisterResponseModel {

 bool get status;@JsonKey(name: 'error_message') String? get errorMessage;@JsonKey(name: 'error_code') String? get errorCode; Map<String, dynamic>? get data;@JsonKey(name: 'api_version') String get apiVersion;@JsonKey(name: 'validation_errors') Map<String, List<String>>? get validationErrors;
/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemberRegisterResponseModelCopyWith<MemberRegisterResponseModel> get copyWith => _$MemberRegisterResponseModelCopyWithImpl<MemberRegisterResponseModel>(this as MemberRegisterResponseModel, _$identity);

  /// Serializes this MemberRegisterResponseModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterResponseModel&&(identical(other.status, status) || other.status == status)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&const DeepCollectionEquality().equals(other.data, data)&&(identical(other.apiVersion, apiVersion) || other.apiVersion == apiVersion)&&const DeepCollectionEquality().equals(other.validationErrors, validationErrors));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,errorMessage,errorCode,const DeepCollectionEquality().hash(data),apiVersion,const DeepCollectionEquality().hash(validationErrors));

@override
String toString() {
  return 'MemberRegisterResponseModel(status: $status, errorMessage: $errorMessage, errorCode: $errorCode, data: $data, apiVersion: $apiVersion, validationErrors: $validationErrors)';
}


}

/// @nodoc
abstract mixin class $MemberRegisterResponseModelCopyWith<$Res>  {
  factory $MemberRegisterResponseModelCopyWith(MemberRegisterResponseModel value, $Res Function(MemberRegisterResponseModel) _then) = _$MemberRegisterResponseModelCopyWithImpl;
@useResult
$Res call({
 bool status,@JsonKey(name: 'error_message') String? errorMessage,@JsonKey(name: 'error_code') String? errorCode, Map<String, dynamic>? data,@JsonKey(name: 'api_version') String apiVersion,@JsonKey(name: 'validation_errors') Map<String, List<String>>? validationErrors
});




}
/// @nodoc
class _$MemberRegisterResponseModelCopyWithImpl<$Res>
    implements $MemberRegisterResponseModelCopyWith<$Res> {
  _$MemberRegisterResponseModelCopyWithImpl(this._self, this._then);

  final MemberRegisterResponseModel _self;
  final $Res Function(MemberRegisterResponseModel) _then;

/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? errorMessage = freezed,Object? errorCode = freezed,Object? data = freezed,Object? apiVersion = null,Object? validationErrors = freezed,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,apiVersion: null == apiVersion ? _self.apiVersion : apiVersion // ignore: cast_nullable_to_non_nullable
as String,validationErrors: freezed == validationErrors ? _self.validationErrors : validationErrors // ignore: cast_nullable_to_non_nullable
as Map<String, List<String>>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _MemberRegisterResponseModel implements MemberRegisterResponseModel {
  const _MemberRegisterResponseModel({required this.status, @JsonKey(name: 'error_message') this.errorMessage, @JsonKey(name: 'error_code') this.errorCode, final  Map<String, dynamic>? data, @JsonKey(name: 'api_version') required this.apiVersion, @JsonKey(name: 'validation_errors') final  Map<String, List<String>>? validationErrors}): _data = data,_validationErrors = validationErrors;
  factory _MemberRegisterResponseModel.fromJson(Map<String, dynamic> json) => _$MemberRegisterResponseModelFromJson(json);

@override final  bool status;
@override@JsonKey(name: 'error_message') final  String? errorMessage;
@override@JsonKey(name: 'error_code') final  String? errorCode;
 final  Map<String, dynamic>? _data;
@override Map<String, dynamic>? get data {
  final value = _data;
  if (value == null) return null;
  if (_data is EqualUnmodifiableMapView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'api_version') final  String apiVersion;
 final  Map<String, List<String>>? _validationErrors;
@override@JsonKey(name: 'validation_errors') Map<String, List<String>>? get validationErrors {
  final value = _validationErrors;
  if (value == null) return null;
  if (_validationErrors is EqualUnmodifiableMapView) return _validationErrors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MemberRegisterResponseModelCopyWith<_MemberRegisterResponseModel> get copyWith => __$MemberRegisterResponseModelCopyWithImpl<_MemberRegisterResponseModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MemberRegisterResponseModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MemberRegisterResponseModel&&(identical(other.status, status) || other.status == status)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&const DeepCollectionEquality().equals(other._data, _data)&&(identical(other.apiVersion, apiVersion) || other.apiVersion == apiVersion)&&const DeepCollectionEquality().equals(other._validationErrors, _validationErrors));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,errorMessage,errorCode,const DeepCollectionEquality().hash(_data),apiVersion,const DeepCollectionEquality().hash(_validationErrors));

@override
String toString() {
  return 'MemberRegisterResponseModel(status: $status, errorMessage: $errorMessage, errorCode: $errorCode, data: $data, apiVersion: $apiVersion, validationErrors: $validationErrors)';
}


}

/// @nodoc
abstract mixin class _$MemberRegisterResponseModelCopyWith<$Res> implements $MemberRegisterResponseModelCopyWith<$Res> {
  factory _$MemberRegisterResponseModelCopyWith(_MemberRegisterResponseModel value, $Res Function(_MemberRegisterResponseModel) _then) = __$MemberRegisterResponseModelCopyWithImpl;
@override @useResult
$Res call({
 bool status,@JsonKey(name: 'error_message') String? errorMessage,@JsonKey(name: 'error_code') String? errorCode, Map<String, dynamic>? data,@JsonKey(name: 'api_version') String apiVersion,@JsonKey(name: 'validation_errors') Map<String, List<String>>? validationErrors
});




}
/// @nodoc
class __$MemberRegisterResponseModelCopyWithImpl<$Res>
    implements _$MemberRegisterResponseModelCopyWith<$Res> {
  __$MemberRegisterResponseModelCopyWithImpl(this._self, this._then);

  final _MemberRegisterResponseModel _self;
  final $Res Function(_MemberRegisterResponseModel) _then;

/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? errorMessage = freezed,Object? errorCode = freezed,Object? data = freezed,Object? apiVersion = null,Object? validationErrors = freezed,}) {
  return _then(_MemberRegisterResponseModel(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,apiVersion: null == apiVersion ? _self.apiVersion : apiVersion // ignore: cast_nullable_to_non_nullable
as String,validationErrors: freezed == validationErrors ? _self._validationErrors : validationErrors // ignore: cast_nullable_to_non_nullable
as Map<String, List<String>>?,
  ));
}


}

// dart format on
