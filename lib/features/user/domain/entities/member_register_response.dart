import 'package:equatable/equatable.dart';

/// Entity representing a member registration response
class MemberRegisterResponse extends Equatable {
  final bool status;
  final String? errorMessage;
  final String? errorCode;
  final Map<String, dynamic>? data;
  final String apiVersion;
  final Map<String, List<String>>? validationErrors;

  const MemberRegisterResponse({
    required this.status,
    this.errorMessage,
    this.errorCode,
    this.data,
    required this.apiVersion,
    this.validationErrors,
  });

  /// Check if registration was successful
  bool get isSuccess => status && errorMessage == null;

  /// Check if there are validation errors
  bool get hasValidationErrors => validationErrors != null && validationErrors!.isNotEmpty;

  @override
  List<Object?> get props => [
        status,
        errorMessage,
        errorCode,
        data,
        apiVersion,
        validationErrors,
      ];
}
