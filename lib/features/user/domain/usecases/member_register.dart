import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/domain/entities/member_register_request.dart';
import 'package:mcdc/features/user/domain/entities/member_register_response.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

/// Parameters for member registration
class MemberRegisterParams extends Equatable {
  final MemberRegisterRequest request;

  const MemberRegisterParams({
    required this.request,
  });

  @override
  List<Object> get props => [request];
}

/// Use case for member registration
class MemberReg<PERSON> extends UseCase<MemberRegisterResponse, MemberRegisterParams> {
  final UserRepository _userRepository;

  MemberRegister(this._userRepository);

  @override
  Future<Either<Failure, MemberRegisterResponse>> call(
    MemberRegisterParams params,
  ) async {
    return await _userRepository.memberRegister(
      request: params.request,
    );
  }
}
