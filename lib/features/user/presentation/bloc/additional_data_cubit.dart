import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/features/master_data/domain/entities/department.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_member_types.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_government_sectors.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_ministries_by_government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_departments_by_ministry.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/master_data/domain/entities/ministry.dart';
import '../../domain/entities/additional_data.dart';
import '../../domain/entities/organization.dart';
import 'additional_data_state.dart';

class AdditionalDataCubit extends Cubit<AdditionalDataState> {
  final GetMemberTypes _getMemberTypes;
  final GetGovernmentSectors _getGovernmentSectors;
  final GetMinistriesByGovernmentSector _getMinistriesByGovernmentSector;
  final GetDepartmentsByMinistry _getDepartmentsByMinistry;

  AdditionalDataCubit(
    this._getMemberTypes,
    this._getGovernmentSectors,
    this._getMinistriesByGovernmentSector,
    this._getDepartmentsByMinistry,
  ) : super(const AdditionalDataState.initial());

  // Current form data
  String _firstName = '';
  String _lastName = '';
  MemberType? _selectedMemberType;
  GovernmentSector? _selectedGovernmentSector;
  String? _governmentSectorOther;
  Ministry? _selectedMinistry;
  String? _ministryOther;
  Department? _selectedDepartment;
  String? _departmentOther;
  String _organizationName = '';

  // Available options
  List<MemberType> _memberTypes = [];
  List<GovernmentSector> _governmentAgencies = [];
  List<Ministry> _ministries = [];
  List<Department> _departments = [];

  // Getters for current form data
  String get firstName => _firstName;
  String get lastName => _lastName;
  MemberType? get selectedMemberType => _selectedMemberType;
  GovernmentSector? get selectedGovernmentSector => _selectedGovernmentSector;
  Ministry? get selectedMinistry => _selectedMinistry;
  Department? get selectedDepartment => _selectedDepartment;
  String get organizationName => _organizationName;

  // Getters for available options
  List<MemberType> get memberTypes => _memberTypes;
  List<GovernmentSector> get governmentAgencies => _governmentAgencies;
  List<Ministry> get ministries => _ministries;
  List<Department> get departments => _departments;

  /// Initialize with data from API
  void initialize() async {
    emit(const AdditionalDataState.loading());

    try {
      // Get member types from API
      final memberTypesResult = await _getMemberTypes(NoParams());

      // Get government sectors from API
      final governmentSectorsResult = await _getGovernmentSectors(NoParams());

      // Check if both API calls succeeded
      if (memberTypesResult.isLeft() || governmentSectorsResult.isLeft()) {
        final memberTypesFailure = memberTypesResult.fold(
          (l) => l,
          (r) => null,
        );
        final governmentSectorsFailure = governmentSectorsResult.fold(
          (l) => l,
          (r) => null,
        );

        final errorMessage =
            memberTypesFailure?.message ??
            governmentSectorsFailure?.message ??
            'Failed to load data';
        emit(AdditionalDataState.error(message: errorMessage));
        return;
      }

      // Extract successful results
      final memberTypes = memberTypesResult.fold(
        (l) => <MemberType>[],
        (r) => r,
      );
      final governmentSectors = governmentSectorsResult.fold(
        (l) => <GovernmentSector>[],
        (r) => r,
      );

      _memberTypes = memberTypes;
      _governmentAgencies = governmentSectors;

      // Initialize empty ministries and departments - will be loaded when selections are made
      _ministries = [];
      _departments = [];

      emit(
        AdditionalDataState.loaded(
          governmentAgencies: _governmentAgencies,
          ministries: _ministries,
          departments: _departments,
          selectedMemberType: _selectedMemberType,
          selectedGovernmentAgency: _selectedGovernmentSector,
          selectedMinistry: _selectedMinistry,
          selectedDepartment: _selectedDepartment,
        ),
      );
    } catch (e) {
      emit(AdditionalDataState.error(message: e.toString()));
    }
  }

  /// Update first name
  void updateFirstName(String value) {
    _firstName = value;
    _clearFieldError('firstName');
  }

  /// Update last name
  void updateLastName(String value) {
    _lastName = value;
    _clearFieldError('lastName');
  }

  /// Update member type
  void updateMemberType(MemberType? value) {
    _selectedMemberType = value;
    _clearFieldError('memberType');

    // Clear government-related selections if private sector is selected (id: 2)
    if (value?.id == 2) {
      _selectedGovernmentSector = null;
      _selectedMinistry = null;
      _selectedDepartment = null;
    }

    // Emit current state to trigger UI rebuild for conditional visibility
    _emitCurrentState();
  }

  // Backward compatibility methods
  /// @deprecated Use updateMemberType instead
  void updateSectorType(dynamic value) {
    if (value is MemberType) {
      updateMemberType(value);
    }
  }

  /// @deprecated Use selectedMemberType instead
  MemberType? get selectedSectorType => _selectedMemberType;

  /// @deprecated Use memberTypes instead
  List<MemberType> get sectorTypes => _memberTypes;

  /// Update government agency
  void updateGovernmentSector(GovernmentSector? value) {
    _selectedGovernmentSector = value;
    _clearFieldError('governmentSector');

    // Clear ministry and department selections when government agency changes
    _selectedMinistry = null;
    _selectedDepartment = null;

    // Fetch ministries for the selected government sector
    if (value != null) {
      _fetchMinistriesByGovernmentSector(value.id);
    } else {
      _ministries = [];
      _departments = [];
      _emitCurrentState();
    }
  }

  /// Update government sector other
  void updateGovernmentSectorOther(String value) {
    _governmentSectorOther = value;
    _clearFieldError('governmentSectorOther');
  }

  /// Update ministry other
  void updateMinistryOther(String value) {
    _ministryOther = value;
    _clearFieldError('ministryOther');
  }

  /// Update department other
  void updateDepartmentOther(String value) {
    _departmentOther = value;
    _clearFieldError('departmentOther');
  }

  /// Update ministry
  void updateMinistry(Ministry? value) {
    _selectedMinistry = value;

    // Clear department selection when ministry changes
    _selectedDepartment = null;

    // Fetch departments for the selected ministry
    if (value != null) {
      _fetchDepartmentsByMinistry(value.id);
    } else {
      _departments = [];
      _emitCurrentState();
    }
  }

  /// Update department
  void updateDepartment(Department? value) {
    _selectedDepartment = value;

    // Emit current state to trigger UI rebuild for conditional visibility
    _emitCurrentState();
  }

  /// Update department name
  void updateOrganizationName(String value) {
    _organizationName = value;
    _clearFieldError('organizationName');
  }

  /// Validate form and return validation result
  bool validateForm(AppLocalizations l10n) {
    final errors = <String, String>{};

    // Validate first name
    if (_firstName.trim().isEmpty) {
      errors['firstName'] = l10n.validateFirstNameRequired;
    }

    // Validate last name
    if (_lastName.trim().isEmpty) {
      errors['lastName'] = l10n.validateLastNameRequired;
    }

    // Validate member type
    if (_selectedMemberType == null) {
      errors['memberType'] =
          l10n.validateSectorTypeRequired; // Reusing existing localization key
    }

    // Validate government agency (only for government sector - id: 1)
    if (_selectedMemberType?.id == 1 && _selectedGovernmentSector == null) {
      errors['governmentSector'] = l10n.validateGovernmentAgencyRequired;
    }

    // Validate department name
    if (_organizationName.trim().isEmpty) {
      errors['organizationName'] = l10n.validateDepartmentNameRequired;
    }

    if (errors.isNotEmpty) {
      emit(
        AdditionalDataState.validationError(
          firstNameError: errors['firstName'],
          lastNameError: errors['lastName'],
          sectorTypeError:
              errors['memberType'], // Using memberType error for sectorType field
          governmentSectorError: errors['governmentAgency'],
          departmentNameError: errors['organizationName'],
        ),
      );
      return false;
    }

    emit(const AdditionalDataState.valid());
    return true;
  }

  /// Get current additional data
  AdditionalData? getAdditionalData() {
    if (_selectedMemberType == null) return null;

    return AdditionalData(
      firstName: _firstName.trim(),
      lastName: _lastName.trim(),
      memberType: _selectedMemberType!,
      governmentAgency: _selectedGovernmentSector,
      ministry: _selectedMinistry,
      department: _selectedDepartment,
      departmentName: _organizationName.trim(),
    );
  }

  /// Clear field error when user starts typing
  void _clearFieldError(String fieldName) {
    if (state is AdditionalDataValidationError) {
      final currentState = state as AdditionalDataValidationError;

      switch (fieldName) {
        case 'firstName':
          if (currentState.firstNameError != null) {
            emit(currentState.copyWith(firstNameError: null));
          }
          break;
        case 'lastName':
          if (currentState.lastNameError != null) {
            emit(currentState.copyWith(lastNameError: null));
          }
          break;
        case 'memberType':
        case 'sectorType': // Keep backward compatibility
          if (currentState.sectorTypeError != null) {
            emit(currentState.copyWith(sectorTypeError: null));
          }
          break;
        case 'governmentSector':
          if (currentState.governmentSectorError != null) {
            emit(currentState.copyWith(governmentSectorError: null));
          }
          break;
        case 'organizationName':
          if (currentState.departmentNameError != null) {
            emit(currentState.copyWith(departmentNameError: null));
          }
          break;
      }
    }
  }

  /// Get field error message
  String? getFieldError(String fieldName) {
    if (state is AdditionalDataValidationError) {
      final errorState = state as AdditionalDataValidationError;
      switch (fieldName) {
        case 'firstName':
          return errorState.firstNameError;
        case 'lastName':
          return errorState.lastNameError;
        case 'sectorType':
          return errorState.sectorTypeError;
        case 'governmentSector':
          return errorState.governmentSectorError;
        case 'organizationName':
          return errorState.departmentNameError;
        default:
          return null;
      }
    }
    return null;
  }

  /// Reset form data
  void reset() {
    _firstName = '';
    _lastName = '';
    _selectedMemberType = null;
    _selectedGovernmentSector = null;
    _selectedMinistry = null;
    _selectedDepartment = null;
    _organizationName = '';
    emit(const AdditionalDataState.initial());
  }

  /// Fetch ministries by government sector ID
  Future<void> _fetchMinistriesByGovernmentSector(
    int governmentSectorId,
  ) async {
    try {
      final result = await _getMinistriesByGovernmentSector(governmentSectorId);

      result.fold(
        (failure) {
          // On failure, keep empty ministries list
          _ministries = [];
          _departments = [];
        },
        (ministries) {
          _ministries = ministries;
          _departments = []; // Clear departments when ministries change
        },
      );

      _emitCurrentState();
    } catch (e) {
      // On error, keep empty ministries list
      _ministries = [];
      _departments = [];
      _emitCurrentState();
    }
  }

  /// Fetch departments by ministry ID
  Future<void> _fetchDepartmentsByMinistry(int ministryId) async {
    try {
      final result = await _getDepartmentsByMinistry(ministryId);

      result.fold(
        (failure) {
          // On failure, keep empty departments list
          _departments = [];
        },
        (departments) {
          _departments = departments;
        },
      );

      _emitCurrentState();
    } catch (e) {
      // On error, keep empty departments list
      _departments = [];
      _emitCurrentState();
    }
  }

  /// Emit current state with loaded data
  void _emitCurrentState() {
    emit(
      AdditionalDataState.loaded(
        governmentAgencies: _governmentAgencies,
        ministries: _ministries,
        departments: _departments,
        selectedMemberType: _selectedMemberType,
        selectedGovernmentAgency: _selectedGovernmentSector,
        selectedMinistry: _selectedMinistry,
        selectedDepartment: _selectedDepartment,
      ),
    );
  }
}
