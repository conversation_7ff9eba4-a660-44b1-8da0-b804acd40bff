import 'package:freezed_annotation/freezed_annotation.dart';

part 'member_register_state.freezed.dart';

@freezed
abstract class MemberRegisterState with _$MemberRegisterState {
  const factory MemberRegisterState.idle() = MemberRegisterIdle;
  
  const factory MemberRegisterState.loading() = MemberRegisterLoading;
  
  const factory MemberRegisterState.success() = MemberRegisterSuccess;
  
  const factory MemberRegisterState.error({
    required String message,
    String? errorCode,
    Map<String, List<String>>? validationErrors,
  }) = MemberRegisterError;
}
